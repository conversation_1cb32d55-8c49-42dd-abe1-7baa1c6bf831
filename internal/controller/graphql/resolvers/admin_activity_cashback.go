package resolvers

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	repo_activity_cashback "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

// CreateTask creates a new activity task (Admin only)
func (r *ActivityCashbackResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	adminUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	// TODO: Add admin role check here
	// For now, we assume the user is admin if they have a valid token

	adminService := activity_cashback.NewAdminService()

	// Parse category ID
	categoryID, err := strconv.ParseUint(input.CategoryID, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid category ID: %w", err)
	}

	// Create task model
	task := &model.ActivityTask{
		CategoryID: uint(categoryID),
		Name:       input.Name,
		TaskType:   model.TaskType(input.TaskType),
		Frequency:  model.TaskFrequency(input.Frequency),
		Points:     input.Points,
		IsActive:   true,
		SortOrder:  0, // Default value
	}

	// Set optional fields
	if input.SortOrder != nil {
		task.SortOrder = *input.SortOrder
	}

	if input.Description != nil {
		task.Description = input.Description
	}
	if input.MaxCompletions != nil {
		task.MaxCompletions = input.MaxCompletions
	}
	if input.ResetPeriod != nil {
		resetPeriod := model.ResetPeriod(*input.ResetPeriod)
		task.ResetPeriod = &resetPeriod
	}
	if input.ActionTarget != nil {
		task.ActionTarget = input.ActionTarget
	}
	if input.VerificationMethod != nil {
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		task.VerificationMethod = &verificationMethod
	}
	if input.ExternalLink != nil {
		task.ExternalLink = input.ExternalLink
	}
	if input.StartDate != nil {
		task.StartDate = input.StartDate
	}
	if input.EndDate != nil {
		task.EndDate = input.EndDate
	}

	// Create the task
	if err := adminService.CreateTask(ctx, task, adminUUID); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err))
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	// Convert to GraphQL model
	gqlTask := convertActivityTaskToGQL(task)
	return gqlTask, nil
}

// UpdateTask updates an existing activity task (Admin only)
func (r *ActivityCashbackResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	adminUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	adminService := activity_cashback.NewAdminService()

	// Parse task ID
	taskUUID, err := uuid.Parse(input.ID)
	if err != nil {
		return nil, fmt.Errorf("invalid task ID: %w", err)
	}

	// Get existing task
	tasks, err := adminService.GetAllTasks(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tasks: %w", err)
	}

	var existingTask *model.ActivityTask
	for _, task := range tasks {
		if task.ID == taskUUID {
			existingTask = &task
			break
		}
	}

	if existingTask == nil {
		return nil, fmt.Errorf("task not found")
	}

	// Update fields if provided
	if input.CategoryID != nil {
		categoryID, err := strconv.ParseUint(*input.CategoryID, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("invalid category ID: %w", err)
		}
		existingTask.CategoryID = uint(categoryID)
	}
	if input.Name != nil {
		existingTask.Name = *input.Name
	}
	if input.Description != nil {
		existingTask.Description = input.Description
	}
	if input.TaskType != nil {
		existingTask.TaskType = model.TaskType(*input.TaskType)
	}
	if input.Frequency != nil {
		existingTask.Frequency = model.TaskFrequency(*input.Frequency)
	}
	if input.Points != nil {
		existingTask.Points = *input.Points
	}
	if input.MaxCompletions != nil {
		existingTask.MaxCompletions = input.MaxCompletions
	}
	if input.ResetPeriod != nil {
		resetPeriod := model.ResetPeriod(*input.ResetPeriod)
		existingTask.ResetPeriod = &resetPeriod
	}
	if input.ActionTarget != nil {
		existingTask.ActionTarget = input.ActionTarget
	}
	if input.VerificationMethod != nil {
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		existingTask.VerificationMethod = &verificationMethod
	}
	if input.ExternalLink != nil {
		existingTask.ExternalLink = input.ExternalLink
	}
	if input.IsActive != nil {
		existingTask.IsActive = *input.IsActive
	}
	if input.StartDate != nil {
		existingTask.StartDate = input.StartDate
	}
	if input.EndDate != nil {
		existingTask.EndDate = input.EndDate
	}
	if input.SortOrder != nil {
		existingTask.SortOrder = *input.SortOrder
	}

	// Update the task
	if err := adminService.UpdateTask(ctx, existingTask, adminUUID); err != nil {
		global.GVA_LOG.Error("Failed to update task", zap.Error(err))
		return nil, fmt.Errorf("failed to update task: %w", err)
	}

	// Convert to GraphQL model
	gqlTask := convertActivityTaskToGQL(existingTask)
	return gqlTask, nil
}

// DeleteTask deletes an activity task (Admin only)
func (r *ActivityCashbackResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return false, utils.ErrAccessTokenInvalid
	}

	adminService := activity_cashback.NewAdminService()

	// Parse task ID
	taskUUID, err := uuid.Parse(taskID)
	if err != nil {
		return false, fmt.Errorf("invalid task ID: %w", err)
	}

	// Delete the task
	if err := adminService.DeleteTask(ctx, taskUUID); err != nil {
		global.GVA_LOG.Error("Failed to delete task", zap.Error(err))
		return false, fmt.Errorf("failed to delete task: %w", err)
	}

	return true, nil
}

// CreateTierBenefit creates a new tier benefit (Admin only)
func (r *ActivityCashbackResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Access token invalid",
		}, nil
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid user ID format",
		}, nil
	}

	_, err := uuid.Parse(userIDStr)
	if err != nil {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid admin ID",
		}, nil
	}

	adminService := activity_cashback.NewAdminService()

	// Create tier benefit model
	benefit := &model.TierBenefit{
		TierLevel:          input.TierLevel,
		TierName:           input.TierName,
		MinPoints:          input.MinPoints,
		CashbackPercentage: decimal.NewFromFloat(input.CashbackPercentage),
		IsActive:           true,
	}

	// Set optional fields
	if input.BenefitsDescription != nil {
		benefit.BenefitsDescription = input.BenefitsDescription
	}
	if input.TierColor != nil {
		benefit.TierColor = input.TierColor
	}
	if input.TierIcon != nil {
		benefit.TierIcon = input.TierIcon
	}

	// Create the tier benefit
	if err := adminService.CreateTierBenefit(ctx, benefit); err != nil {
		global.GVA_LOG.Error("Failed to create tier benefit", zap.Error(err))
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Failed to create tier benefit",
		}, nil
	}

	return &gql_model.TierBenefitResponse{
		Success: true,
		Message: "Tier benefit created successfully",
		Data:    convertTierBenefitToGQL(benefit),
	}, nil
}

// UpdateTierBenefit updates an existing tier benefit (Admin only)
func (r *ActivityCashbackResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Access token invalid",
		}, nil
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid user ID format",
		}, nil
	}

	_, err := uuid.Parse(userIDStr)
	if err != nil {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid admin ID",
		}, nil
	}

	adminService := activity_cashback.NewAdminService()

	// Parse tier benefit ID
	benefitID, err := strconv.ParseUint(input.ID, 10, 32)
	if err != nil {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid tier benefit ID",
		}, nil
	}

	// Get existing tier benefit
	tierBenefitRepo := repo_activity_cashback.NewTierBenefitRepository()
	existingBenefit, err := tierBenefitRepo.GetByID(ctx, uint(benefitID))
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier benefit", zap.Error(err))
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Tier benefit not found",
		}, nil
	}

	// Update fields if provided
	if input.TierLevel != nil {
		existingBenefit.TierLevel = *input.TierLevel
	}
	if input.TierName != nil {
		existingBenefit.TierName = *input.TierName
	}
	if input.MinPoints != nil {
		existingBenefit.MinPoints = *input.MinPoints
	}
	if input.CashbackPercentage != nil {
		existingBenefit.CashbackPercentage = decimal.NewFromFloat(*input.CashbackPercentage)
	}
	if input.BenefitsDescription != nil {
		existingBenefit.BenefitsDescription = input.BenefitsDescription
	}
	if input.TierColor != nil {
		existingBenefit.TierColor = input.TierColor
	}
	if input.TierIcon != nil {
		existingBenefit.TierIcon = input.TierIcon
	}
	if input.IsActive != nil {
		existingBenefit.IsActive = *input.IsActive
	}

	// Update the tier benefit
	if err := adminService.UpdateTierBenefit(ctx, existingBenefit); err != nil {
		global.GVA_LOG.Error("Failed to update tier benefit", zap.Error(err))
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Failed to update tier benefit",
		}, nil
	}

	return &gql_model.TierBenefitResponse{
		Success: true,
		Message: "Tier benefit updated successfully",
		Data:    convertTierBenefitToGQL(existingBenefit),
	}, nil
}

// DeleteTierBenefit deletes a tier benefit (Admin only)
func (r *ActivityCashbackResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return false, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return false, utils.ErrAccessTokenInvalid
	}

	_, err := uuid.Parse(userIDStr)
	if err != nil {
		return false, fmt.Errorf("invalid admin ID: %w", err)
	}

	adminService := activity_cashback.NewAdminService()

	// Parse tier benefit ID
	benefitID, err := strconv.ParseUint(tierBenefitID, 10, 32)
	if err != nil {
		return false, fmt.Errorf("invalid tier benefit ID: %w", err)
	}

	// Delete the tier benefit
	if err := adminService.DeleteTierBenefit(ctx, uint(benefitID)); err != nil {
		global.GVA_LOG.Error("Failed to delete tier benefit", zap.Error(err))
		return false, fmt.Errorf("failed to delete tier benefit: %w", err)
	}

	return true, nil
}

// CreateTaskCategory creates a new task category (Admin only)
func (r *ActivityCashbackResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	// TODO: Add admin role check here
	// For now, we assume the user is admin if they have a valid token

	adminService := activity_cashback.NewAdminService()

	// Create category model
	category := &model.TaskCategory{
		Name:        input.Name,
		DisplayName: input.DisplayName,
		IsActive:    true,
		SortOrder:   0, // Default value
	}

	// Set optional fields
	if input.Description != nil {
		category.Description = input.Description
	}
	if input.Icon != nil {
		category.Icon = input.Icon
	}
	if input.SortOrder != nil {
		category.SortOrder = *input.SortOrder
	}

	// Create the category
	if err := adminService.CreateTaskCategory(ctx, category); err != nil {
		global.GVA_LOG.Error("Failed to create task category", zap.Error(err))
		return nil, fmt.Errorf("failed to create task category: %w", err)
	}

	global.GVA_LOG.Info("Task category created", zap.Uint("category_id", category.ID), zap.String("name", category.Name), zap.String("admin_id", userID.(string)))

	return convertTaskCategoryToGQL(category), nil
}

// UpdateTaskCategory updates an existing task category (Admin only)
func (r *ActivityCashbackResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	adminService := activity_cashback.NewAdminService()

	// Parse category ID
	categoryID, err := strconv.ParseUint(input.ID, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid category ID: %w", err)
	}

	// Get existing category
	categories, err := adminService.GetTaskCategories(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	var existingCategory *model.TaskCategory
	for _, category := range categories {
		if category.ID == uint(categoryID) {
			existingCategory = &category
			break
		}
	}

	if existingCategory == nil {
		return nil, fmt.Errorf("category not found")
	}

	// Update fields if provided
	if input.Name != nil {
		existingCategory.Name = *input.Name
	}
	if input.DisplayName != nil {
		existingCategory.DisplayName = *input.DisplayName
	}
	if input.Description != nil {
		existingCategory.Description = input.Description
	}
	if input.Icon != nil {
		existingCategory.Icon = input.Icon
	}
	if input.SortOrder != nil {
		existingCategory.SortOrder = *input.SortOrder
	}
	if input.IsActive != nil {
		existingCategory.IsActive = *input.IsActive
	}

	// Update the category
	if err := adminService.UpdateTaskCategory(ctx, existingCategory); err != nil {
		global.GVA_LOG.Error("Failed to update task category", zap.Error(err))
		return nil, fmt.Errorf("failed to update task category: %w", err)
	}

	global.GVA_LOG.Info("Task category updated", zap.Uint("category_id", existingCategory.ID), zap.String("name", existingCategory.Name), zap.String("admin_id", userID.(string)))

	return convertTaskCategoryToGQL(existingCategory), nil
}

// DeleteTaskCategory deletes a task category (Admin only)
func (r *ActivityCashbackResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return false, utils.ErrAccessTokenInvalid
	}

	adminService := activity_cashback.NewAdminService()

	// Parse category ID
	catID, err := strconv.ParseUint(categoryID, 10, 32)
	if err != nil {
		return false, fmt.Errorf("invalid category ID: %w", err)
	}

	// Delete the category
	if err := adminService.DeleteTaskCategory(ctx, uint(catID)); err != nil {
		global.GVA_LOG.Error("Failed to delete task category", zap.Error(err))
		return false, fmt.Errorf("failed to delete task category: %w", err)
	}

	global.GVA_LOG.Info("Task category deleted", zap.Uint("category_id", uint(catID)), zap.String("admin_id", userID.(string)))

	return true, nil
}

// TierBenefits retrieves all tier benefits
func (r *ActivityCashbackResolver) TierBenefits(ctx context.Context) (*gql_model.TierBenefitsResponse, error) {
	adminService := activity_cashback.NewAdminService()

	benefits, err := adminService.GetTierBenefits(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier benefits", zap.Error(err))
		return &gql_model.TierBenefitsResponse{
			Success: false,
			Message: "Failed to retrieve tier benefits",
		}, nil
	}

	var gqlBenefits []*gql_model.TierBenefit
	for _, benefit := range benefits {
		gqlBenefits = append(gqlBenefits, convertTierBenefitToGQL(&benefit))
	}

	return &gql_model.TierBenefitsResponse{
		Success: true,
		Message: "Tier benefits retrieved successfully",
		Data:    gqlBenefits,
	}, nil
}

// UserTaskProgress retrieves user task progress
func (r *ActivityCashbackResolver) UserTaskProgress(ctx context.Context) (*gql_model.UserTaskProgressResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	progress, err := service.GetUserTaskProgress(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user task progress", zap.Error(err))
		return &gql_model.UserTaskProgressResponse{
			Success: false,
			Message: "Failed to retrieve task progress",
		}, nil
	}

	var gqlProgress []*gql_model.UserTaskProgress
	for _, p := range progress {
		gqlProgress = append(gqlProgress, convertUserTaskProgressToGQL(&p))
	}

	return &gql_model.UserTaskProgressResponse{
		Success: true,
		Message: "Task progress retrieved successfully",
		Data:    gqlProgress,
	}, nil
}

// TaskCategories retrieves all task categories
func (r *ActivityCashbackResolver) TaskCategories(ctx context.Context) ([]*gql_model.TaskCategory, error) {
	adminService := activity_cashback.NewAdminService()

	categories, err := adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task categories", zap.Error(err))
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}

	var gqlCategories []*gql_model.TaskCategory
	for _, category := range categories {
		gqlCategories = append(gqlCategories, convertTaskCategoryToGQL(&category))
	}

	return gqlCategories, nil
}

// TasksByCategory retrieves tasks by category name
func (r *ActivityCashbackResolver) TasksByCategory(ctx context.Context, categoryName string) ([]*gql_model.ActivityTask, error) {
	service := activity_cashback.NewActivityCashbackService()

	tasks, err := service.GetTasksByCategory(ctx, categoryName)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tasks by category", zap.Error(err))
		return nil, fmt.Errorf("failed to get tasks by category: %w", err)
	}

	var gqlTasks []*gql_model.ActivityTask
	for _, task := range tasks {
		gqlTasks = append(gqlTasks, convertActivityTaskToGQL(&task))
	}

	return gqlTasks, nil
}

// UserTierInfo retrieves user tier information
func (r *ActivityCashbackResolver) UserTierInfo(ctx context.Context) (*gql_model.UserTierInfo, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	tierInfo, err := service.GetUserTierInfo(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user tier info", zap.Error(err))
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}

	return convertUserTierInfoToGQL(tierInfo), nil
}

// TaskCompletionHistory retrieves task completion history
func (r *ActivityCashbackResolver) TaskCompletionHistory(ctx context.Context, input *gql_model.TaskCompletionHistoryInput) (*gql_model.TaskCompletionHistoryResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Set default values
	limit := 10
	offset := 0
	if input != nil {
		if input.Limit != nil {
			limit = *input.Limit
		}
		if input.Offset != nil {
			offset = *input.Offset
		}
	}

	adminService := activity_cashback.NewAdminService()

	// For now, we'll get completion stats instead of detailed history
	// This would need to be implemented in the admin service
	startDate := time.Now().AddDate(0, -1, 0) // Last month
	endDate := time.Now()

	if input != nil {
		if input.StartDate != nil {
			startDate = *input.StartDate
		}
		if input.EndDate != nil {
			endDate = *input.EndDate
		}
	}

	stats, err := adminService.GetTaskCompletionStats(ctx, startDate, endDate)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task completion stats", zap.Error(err))
		return &gql_model.TaskCompletionHistoryResponse{
			Success: false,
			Message: "Failed to retrieve completion history",
		}, nil
	}

	// Convert stats to history format (simplified)
	var gqlHistory []*gql_model.TaskCompletionHistory
	// This would need proper implementation with actual history data
	// For now, we'll use the variables to avoid unused variable errors
	_ = userUUID
	_ = limit
	_ = offset
	_ = stats

	return &gql_model.TaskCompletionHistoryResponse{
		Success: true,
		Message: "Completion history retrieved successfully",
		Data:    gqlHistory,
		Total:   len(gqlHistory),
	}, nil
}
