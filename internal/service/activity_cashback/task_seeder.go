package activity_cashback

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
)

// TaskSeeder seeds initial tasks into the database
type TaskSeeder struct {
	categoryRepo activity_cashback.TaskCategoryRepositoryInterface
	taskRepo     activity_cashback.ActivityTaskRepositoryInterface
}

// NewTaskSeeder creates a new TaskSeeder
func NewTaskSeeder() *TaskSeeder {
	return &TaskSeeder{
		categoryRepo: activity_cashback.NewTaskCategoryRepository(),
		taskRepo:     activity_cashback.NewActivityTaskRepository(),
	}
}

// SeedTasks seeds all initial tasks
func (s *TaskSeeder) SeedTasks(ctx context.Context) error {
	global.GVA_LOG.Info("Starting task seeding")

	// Get categories
	categories, err := s.categoryRepo.GetAll(ctx)
	if err != nil {
		return err
	}

	categoryMap := make(map[string]uint)
	for _, category := range categories {
		categoryMap[category.Name] = category.ID
	}

	// Seed daily tasks
	if err := s.seedDailyTasks(ctx, categoryMap["daily"]); err != nil {
		return err
	}

	// Seed community tasks
	if err := s.seedCommunityTasks(ctx, categoryMap["community"]); err != nil {
		return err
	}

	// Seed trading tasks
	if err := s.seedTradingTasks(ctx, categoryMap["trading"]); err != nil {
		return err
	}

	global.GVA_LOG.Info("Task seeding completed successfully")
	return nil
}

// seedDailyTasks seeds daily tasks
func (s *TaskSeeder) seedDailyTasks(ctx context.Context, categoryID uint) error {
	dailyTasks := []struct {
		name               string
		description        string
		points             int
		taskType           model.TaskType
		frequency          model.TaskFrequency
		resetPeriod        *model.ResetPeriod
		conditions         *model.TaskConditions
		actionTarget       *string
		verificationMethod *model.VerificationMethod
		sortOrder          int
	}{
		{
			name:               "Daily Check-in",
			description:        "Check in daily to earn points",
			points:             5,
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyDaily,
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			actionTarget:       &[]string{"Homepage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          1,
		},
		{
			name:               "Complete 1 MEME Trade",
			description:        "Complete at least 1 MEME trade",
			points:             200,
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyDaily,
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			conditions:         &model.TaskConditions{RequiredTradeCount: &[]int{1}[0]},
			actionTarget:       &[]string{"MEME trading page"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          2,
		},
		{
			name:               "Complete 1 Perpetual Trade",
			description:        "Complete at least 1 perpetual trade",
			points:             200,
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyDaily,
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			conditions:         &model.TaskConditions{RequiredTradeCount: &[]int{1}[0]},
			actionTarget:       &[]string{"Perpetual trading page"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          3,
		},
		{
			name:               "Check Market Conditions",
			description:        "Check market conditions once per day",
			points:             5,
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyDaily,
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			actionTarget:       &[]string{"Market homepage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          4,
		},
		{
			name:               "Consecutive Check-in (3/7/30 days)",
			description:        "Check in consecutively to earn bonus points",
			points:             0, // Points awarded based on streak
			taskType:           model.TaskTypeProgressive,
			frequency:          model.FrequencyProgressive,
			conditions:         &model.TaskConditions{ConsecutiveDays: &[]int{30}[0]},
			actionTarget:       &[]string{"Homepage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          5,
		},
	}

	for _, taskData := range dailyTasks {
		if err := s.createTaskIfNotExists(ctx, categoryID, taskData.name, taskData.description, taskData.points, taskData.taskType, taskData.frequency, taskData.resetPeriod, taskData.conditions, taskData.actionTarget, taskData.verificationMethod, nil, taskData.sortOrder); err != nil {
			return err
		}
	}

	return nil
}

// seedCommunityTasks seeds community tasks
func (s *TaskSeeder) seedCommunityTasks(ctx context.Context, categoryID uint) error {
	communityTasks := []struct {
		name               string
		description        string
		points             int
		taskType           model.TaskType
		frequency          model.TaskFrequency
		verificationMethod *model.VerificationMethod
		externalLink       *string
		sortOrder          int
	}{
		{
			name:               "Follow Twitter",
			description:        "Follow our Twitter account",
			points:             50,
			taskType:           model.TaskTypeOneTime,
			frequency:          model.FrequencyOneTime,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://x.com/intent/follow?screen_name=xbit_dex"}[0],
			sortOrder:          1,
		},
		{
			name:               "Retweet Post",
			description:        "Retweet our latest post",
			points:             10,
			taskType:           model.TaskTypeManualUpdate,
			frequency:          model.FrequencyManual,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://x.com/intent/retweet?tweet_id=*********"}[0],
			sortOrder:          2,
		},
		{
			name:               "Like Post",
			description:        "Like our latest post",
			points:             10,
			taskType:           model.TaskTypeManualUpdate,
			frequency:          model.FrequencyManual,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://x.com/intent/like?tweet_id=*********"}[0],
			sortOrder:          3,
		},
		{
			name:               "Join Telegram",
			description:        "Join our Telegram community",
			points:             30,
			taskType:           model.TaskTypeOneTime,
			frequency:          model.FrequencyOneTime,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://t.me/xbit_dex"}[0],
			sortOrder:          4,
		},
		{
			name:               "Invite Friends",
			description:        "Invite friends to join XBIT",
			points:             100,
			taskType:           model.TaskTypeUnlimited,
			frequency:          model.FrequencyUnlimited,
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          5,
		},
		{
			name:               "Share Referral Link",
			description:        "Share your referral link",
			points:             10,
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyDaily,
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          6,
		},
	}

	for _, taskData := range communityTasks {
		resetPeriod := model.ResetDaily
		if taskData.taskType == model.TaskTypeDaily {
			resetPeriod = model.ResetDaily
		} else {
			resetPeriod = model.ResetNever
		}

		if err := s.createTaskIfNotExists(ctx, categoryID, taskData.name, taskData.description, taskData.points, taskData.taskType, taskData.frequency, &resetPeriod, nil, nil, taskData.verificationMethod, taskData.externalLink, taskData.sortOrder); err != nil {
			return err
		}
	}

	return nil
}

// seedTradingTasks seeds trading tasks
func (s *TaskSeeder) seedTradingTasks(ctx context.Context, categoryID uint) error {
	tradingTasks := []struct {
		name        string
		description string
		points      int
		taskType    model.TaskType
		frequency   model.TaskFrequency
		conditions  *model.TaskConditions
		sortOrder   int
	}{
		{
			name:        "Trading Points",
			description: "Earn points based on trading volume",
			points:      0, // Variable points based on volume
			taskType:    model.TaskTypeUnlimited,
			frequency:   model.FrequencyUnlimited,
			sortOrder:   1,
		},
		{
			name:        "Accumulated Trading $10,000",
			description: "Reach $10,000 in total trading volume",
			points:      300,
			taskType:    model.TaskTypeProgressive,
			frequency:   model.FrequencyProgressive,
			conditions:  &model.TaskConditions{MinTradingVolume: &[]float64{10000}[0]},
			sortOrder:   2,
		},
		{
			name:        "Accumulated Trading $50,000",
			description: "Reach $50,000 in total trading volume",
			points:      1000,
			taskType:    model.TaskTypeProgressive,
			frequency:   model.FrequencyProgressive,
			conditions:  &model.TaskConditions{MinTradingVolume: &[]float64{50000}[0]},
			sortOrder:   3,
		},
		{
			name:        "Accumulated Trading $100,000",
			description: "Reach $100,000 in total trading volume",
			points:      2500,
			taskType:    model.TaskTypeProgressive,
			frequency:   model.FrequencyProgressive,
			conditions:  &model.TaskConditions{MinTradingVolume: &[]float64{100000}[0]},
			sortOrder:   4,
		},
		{
			name:        "Accumulated Trading $500,000",
			description: "Reach $500,000 in total trading volume",
			points:      10000,
			taskType:    model.TaskTypeProgressive,
			frequency:   model.FrequencyProgressive,
			conditions:  &model.TaskConditions{MinTradingVolume: &[]float64{500000}[0]},
			sortOrder:   5,
		},
	}

	for _, taskData := range tradingTasks {
		resetPeriod := model.ResetNever
		verificationMethod := model.VerificationAuto
		actionTarget := "Trading page"

		if err := s.createTaskIfNotExists(ctx, categoryID, taskData.name, taskData.description, taskData.points, taskData.taskType, taskData.frequency, &resetPeriod, taskData.conditions, &actionTarget, &verificationMethod, nil, taskData.sortOrder); err != nil {
			return err
		}
	}

	return nil
}

// createTaskIfNotExists creates a task if it doesn't already exist
func (s *TaskSeeder) createTaskIfNotExists(ctx context.Context, categoryID uint, name, description string, points int, taskType model.TaskType, frequency model.TaskFrequency, resetPeriod *model.ResetPeriod, conditions *model.TaskConditions, actionTarget *string, verificationMethod *model.VerificationMethod, externalLink *string, sortOrder int) error {
	// Check if task already exists
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return err
	}

	for _, task := range tasks {
		if task.Name == name {
			global.GVA_LOG.Debug("Task already exists, skipping", zap.String("name", name))
			return nil
		}
	}

	// Create new task
	task := &model.ActivityTask{
		CategoryID:         categoryID,
		Name:               name,
		Description:        &description,
		TaskType:           taskType,
		Frequency:          frequency,
		Points:             points,
		ResetPeriod:        resetPeriod,
		Conditions:         conditions,
		ActionTarget:       actionTarget,
		VerificationMethod: verificationMethod,
		ExternalLink:       externalLink,
		IsActive:           true,
		SortOrder:          sortOrder,
	}

	if err := s.taskRepo.Create(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err), zap.String("name", name))
		return err
	}

	global.GVA_LOG.Info("Task created successfully", zap.String("name", name), zap.String("task_id", task.ID.String()))
	return nil
}
