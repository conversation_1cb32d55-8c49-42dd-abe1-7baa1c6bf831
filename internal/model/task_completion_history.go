package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"net"
	"time"

	"github.com/google/uuid"
)

// VerificationData represents verification details for task completion
type VerificationData struct {
	VerificationMethod string                 `json:"verification_method"`
	VerifiedAt         time.Time              `json:"verified_at"`
	VerificationSource string                 `json:"verification_source,omitempty"`
	SocialMediaData    map[string]interface{} `json:"social_media_data,omitempty"`
	TradingData        map[string]interface{} `json:"trading_data,omitempty"`
	CustomData         map[string]interface{} `json:"custom_data,omitempty"`
}

// Scan implements the sql.Scanner interface for VerificationData
func (vd *VerificationData) Scan(value interface{}) error {
	if value == nil {
		*vd = VerificationData{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, vd)
}

// Value implements the driver.Valuer interface for VerificationData
func (vd VerificationData) Value() (driver.Value, error) {
	return json.Marshal(vd)
}

// TaskCompletionHistory represents the task_completion_history table
type TaskCompletionHistory struct {
	ID               uuid.UUID         `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           uuid.UUID         `gorm:"type:uuid;not null;index" json:"user_id"`
	TaskID           uuid.UUID         `gorm:"type:uuid;not null;index" json:"task_id"`
	PointsAwarded    int               `gorm:"not null" json:"points_awarded"`
	CompletionDate   time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"completion_date"`
	VerificationData *VerificationData `gorm:"type:jsonb" json:"verification_data"`
	IPAddress        *net.IP           `gorm:"type:inet" json:"ip_address"`
	UserAgent        *string           `gorm:"type:text" json:"user_agent"`
	CreatedAt        time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
}

// TableName specifies the table name for TaskCompletionHistory
func (TaskCompletionHistory) TableName() string {
	return "task_completion_history"
}

// IsVerified checks if the task completion was verified
func (tch *TaskCompletionHistory) IsVerified() bool {
	return tch.VerificationData != nil && tch.VerificationData.VerificationMethod != ""
}

// GetVerificationMethod returns the verification method used
func (tch *TaskCompletionHistory) GetVerificationMethod() string {
	if tch.VerificationData == nil {
		return ""
	}
	return tch.VerificationData.VerificationMethod
}

// SetVerificationData sets the verification data for the completion
func (tch *TaskCompletionHistory) SetVerificationData(method, source string, customData map[string]interface{}) {
	tch.VerificationData = &VerificationData{
		VerificationMethod: method,
		VerifiedAt:         time.Now(),
		VerificationSource: source,
		CustomData:         customData,
	}
}

// SetSocialMediaVerification sets social media verification data
func (tch *TaskCompletionHistory) SetSocialMediaVerification(method, source string, socialData map[string]interface{}) {
	tch.VerificationData = &VerificationData{
		VerificationMethod: method,
		VerifiedAt:         time.Now(),
		VerificationSource: source,
		SocialMediaData:    socialData,
	}
}

// SetTradingVerification sets trading verification data
func (tch *TaskCompletionHistory) SetTradingVerification(method string, tradingData map[string]interface{}) {
	tch.VerificationData = &VerificationData{
		VerificationMethod: method,
		VerifiedAt:         time.Now(),
		VerificationSource: "trading_system",
		TradingData:        tradingData,
	}
}
