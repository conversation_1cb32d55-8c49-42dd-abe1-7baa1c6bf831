package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// TaskCompletionHistoryRepository implements TaskCompletionHistoryRepositoryInterface
type TaskCompletionHistoryRepository struct {
	db *gorm.DB
}

// NewTaskCompletionHistoryRepository creates a new TaskCompletionHistoryRepository
func NewTaskCompletionHistoryRepository() TaskCompletionHistoryRepositoryInterface {
	return &TaskCompletionHistoryRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new task completion history record
func (r *TaskCompletionHistoryRepository) Create(ctx context.Context, history *model.TaskCompletionHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

// GetByUserID retrieves task completion history by user ID with pagination
func (r *TaskCompletionHistoryRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.TaskCompletionHistory, error) {
	var history []model.TaskCompletionHistory
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ?", userID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&history).Error
	return history, err
}

// GetByTaskID retrieves task completion history by task ID with pagination
func (r *TaskCompletionHistoryRepository) GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.TaskCompletionHistory, error) {
	var history []model.TaskCompletionHistory
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("task_id = ?", taskID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&history).Error
	return history, err
}

// GetByUserAndTask retrieves task completion history by user and task with pagination
func (r *TaskCompletionHistoryRepository) GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.TaskCompletionHistory, error) {
	var history []model.TaskCompletionHistory
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Order("completion_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&history).Error
	return history, err
}

// GetUserCompletionStats retrieves completion statistics for a user within a date range
func (r *TaskCompletionHistoryRepository) GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	var results []struct {
		TaskName string `json:"task_name"`
		Count    int    `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Table("task_completion_history tch").
		Select("at.name as task_name, COUNT(*) as count").
		Joins("JOIN activity_tasks at ON tch.task_id = at.id").
		Where("tch.user_id = ? AND tch.completion_date BETWEEN ? AND ?", userID, startDate, endDate).
		Group("at.name").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int)
	for _, result := range results {
		stats[result.TaskName] = result.Count
	}

	return stats, nil
}

// GetTaskCompletionStats retrieves completion statistics for a specific task within a date range
func (r *TaskCompletionHistoryRepository) GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.TaskCompletionHistory{}).
		Where("task_id = ? AND completion_date BETWEEN ? AND ?", taskID, startDate, endDate).
		Count(&count).Error
	return int(count), err
}

// GetDailyCompletionStats retrieves daily completion statistics for all tasks on a specific date
func (r *TaskCompletionHistoryRepository) GetDailyCompletionStats(ctx context.Context, date time.Time) (map[uuid.UUID]int, error) {
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	var results []struct {
		TaskID uuid.UUID `json:"task_id"`
		Count  int       `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.TaskCompletionHistory{}).
		Select("task_id, COUNT(*) as count").
		Where("completion_date BETWEEN ? AND ?", startOfDay, endOfDay).
		Group("task_id").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[uuid.UUID]int)
	for _, result := range results {
		stats[result.TaskID] = result.Count
	}

	return stats, nil
}
